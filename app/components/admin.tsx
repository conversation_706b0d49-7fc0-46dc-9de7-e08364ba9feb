import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import styles from "./admin.module.scss";
import { useAuthStore } from "../store/auth";
import { Path } from "../constant";
import AdminIcon from "../icons/admin.svg";
import LeftIcon from "../icons/left.svg";
import SettingsIcon from "../icons/settings.svg";
import UserIcon from "../icons/user.svg";
import clsx from "clsx";

// 管理面板菜单项
enum AdminTab {
  Dashboard = "dashboard",
  Providers = "providers",
  Users = "users",
  Settings = "settings",
}

interface AdminMenuItem {
  key: AdminTab;
  label: string;
  icon: React.ReactNode;
  description: string;
}

const adminMenuItems: AdminMenuItem[] = [
  {
    key: AdminTab.Dashboard,
    label: "仪表盘",
    icon: <AdminIcon />,
    description: "系统概览和统计信息",
  },
  {
    key: AdminTab.Providers,
    label: "服务商管理",
    icon: <SettingsIcon />,
    description: "API 配置和模型管理",
  },
  {
    key: AdminTab.Users,
    label: "用户管理",
    icon: <UserIcon />,
    description: "用户列表和权限设置",
  },
  {
    key: AdminTab.Settings,
    label: "系统设置",
    icon: <SettingsIcon />,
    description: "全局参数配置",
  },
];

export function AdminPage() {
  const navigate = useNavigate();
  const authStore = useAuthStore();
  const [currentTab, setCurrentTab] = useState<AdminTab>(AdminTab.Dashboard);

  // 检查管理员权限
  useEffect(() => {
    if (!authStore.user) {
      navigate(Path.Auth);
      return;
    }

    if (!authStore.isAdmin()) {
      navigate(Path.Home);
      return;
    }
  }, [authStore.user, navigate]);

  // 如果不是管理员，不渲染内容
  if (!authStore.user || !authStore.isAdmin()) {
    return null;
  }

  const currentMenuItem = adminMenuItems.find(
    (item) => item.key === currentTab,
  );

  const renderTabContent = () => {
    switch (currentTab) {
      case AdminTab.Dashboard:
        return <DashboardContent />;
      case AdminTab.Providers:
        return <ProvidersContent />;
      case AdminTab.Users:
        return <UsersContent />;
      case AdminTab.Settings:
        return <SettingsContent />;
      default:
        return <DashboardContent />;
    }
  };

  return (
    <div className={styles["admin-page"]}>
      {/* 左侧边栏 */}
      <div className={styles["admin-sidebar"]}>
        <div className={styles["admin-header"]}>
          <div className={styles["admin-title"]}>
            <AdminIcon />
            管理面板
          </div>
          <div className={styles["admin-subtitle"]}>QADChat 系统管理</div>
        </div>

        <nav className={styles["admin-nav"]}>
          {adminMenuItems.map((item) => (
            <button
              key={item.key}
              className={clsx(
                styles["admin-nav-item"],
                currentTab === item.key && styles.active,
              )}
              onClick={() => setCurrentTab(item.key)}
            >
              {item.icon}
              <span>{item.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* 右侧内容区域 */}
      <div className={styles["admin-content"]}>
        <div className={styles["admin-content-header"]}>
          <div className={styles["admin-content-title"]}>
            {currentMenuItem?.label}
          </div>
          <div className={styles["admin-content-subtitle"]}>
            {currentMenuItem?.description}
          </div>
        </div>

        <div className={styles["admin-content-body"]}>
          <a
            href="#"
            className={styles["admin-back-button"]}
            onClick={(e) => {
              e.preventDefault();
              navigate(Path.Home);
            }}
          >
            <LeftIcon />
            返回主页
          </a>

          {renderTabContent()}
        </div>
      </div>
    </div>
  );
}

// 仪表盘内容
function DashboardContent() {
  return (
    <>
      <div className={styles["admin-stats"]}>
        <div className={styles["admin-stat-card"]}>
          <div className="stat-value">0</div>
          <div className="stat-label">注册用户</div>
        </div>
        <div className={styles["admin-stat-card"]}>
          <div className="stat-value">0</div>
          <div className="stat-label">活跃服务商</div>
        </div>
        <div className={styles["admin-stat-card"]}>
          <div className="stat-value">0</div>
          <div className="stat-label">今日请求</div>
        </div>
        <div className={styles["admin-stat-card"]}>
          <div className="stat-value">0</div>
          <div className="stat-label">系统配置</div>
        </div>
      </div>

      <div className={styles["admin-card"]}>
        <h3>欢迎使用 QADChat 管理面板</h3>
        <p>
          这里是系统管理的中心，您可以管理用户、配置服务商、查看系统统计信息等。
          请从左侧菜单选择您要管理的功能模块。
        </p>
        <p>当前版本支持的功能：</p>
        <ul>
          <li>用户注册和登录管理</li>
          <li>AI 服务商配置管理</li>
          <li>系统参数设置</li>
          <li>基础统计信息查看</li>
        </ul>
      </div>
    </>
  );
}

// 服务商管理内容
function ProvidersContent() {
  return (
    <div className={styles["admin-card"]}>
      <h3>服务商管理</h3>
      <p>在这里管理 AI 服务商的配置，包括 API Key、模型设置等。</p>
      <p>功能开发中...</p>
    </div>
  );
}

// 用户管理内容
function UsersContent() {
  return (
    <div className={styles["admin-card"]}>
      <h3>用户管理</h3>
      <p>查看和管理系统中的所有用户，设置用户权限。</p>
      <p>功能开发中...</p>
    </div>
  );
}

// 系统设置内容
function SettingsContent() {
  return (
    <div className={styles["admin-card"]}>
      <h3>系统设置</h3>
      <p>配置系统的全局参数和设置。</p>
      <p>功能开发中...</p>
    </div>
  );
}
