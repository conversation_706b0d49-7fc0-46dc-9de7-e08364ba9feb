import styles from "./auth.module.scss";
import { IconButton } from "./button";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Path } from "../constant";
import { useAuthStore } from "../store/auth";
import Locale from "../locales";
import BotIcon from "../icons/bot.svg";
import { PasswordInput, Input } from "./ui-lib";
import LeftIcon from "@/app/icons/left.svg";
import clsx from "clsx";

export function AuthPage() {
  const navigate = useNavigate();
  const authStore = useAuthStore();
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState("");
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // 如果已经登录，重定向到首页
  useEffect(() => {
    if (authStore.user) {
      navigate(Path.Home);
    }
  }, [authStore.user, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    if (!email || !password) {
      setError("请填写所有必填字段");
      return;
    }

    if (!isLogin && !username) {
      setError("请填写用户名");
      return;
    }

    try {
      let result;
      if (isLogin) {
        result = await authStore.login(email, password);
      } else {
        result = await authStore.register(email, username, password);
      }

      if (result.success) {
        setSuccess(isLogin ? "登录成功！" : "注册成功！");
        setTimeout(() => {
          navigate(Path.Home);
        }, 1000);
      } else {
        setError(result.error || "操作失败");
      }
    } catch (error) {
      setError("网络错误，请稍后重试");
    }
  };

  const switchMode = () => {
    setIsLogin(!isLogin);
    setError("");
    setSuccess("");
    setEmail("");
    setUsername("");
    setPassword("");
  };

  return (
    <div className={styles["auth-page"]}>
      <div className={styles["auth-container"]}>
        <div className={styles["auth-header"]}>
          <IconButton
            icon={<LeftIcon />}
            text="返回"
            onClick={() => navigate(Path.Home)}
          />
        </div>

        <div className={clsx("no-dark", styles["auth-logo"])}>
          <BotIcon />
        </div>

        <div className={styles["auth-title"]}>
          {isLogin ? "登录 QADChat" : "注册 QADChat"}
        </div>
        <div className={styles["auth-tips"]}>
          {isLogin ? "欢迎回来！请登录您的账号" : "创建您的 QADChat 账号"}
        </div>

        {error && <div className={styles["auth-error"]}>{error}</div>}

        {success && <div className={styles["auth-success"]}>{success}</div>}

        <form onSubmit={handleSubmit}>
          <Input
            className={styles["auth-input"]}
            type="email"
            placeholder="邮箱地址"
            value={email}
            onChange={(e) => setEmail((e.target as HTMLInputElement).value)}
            required
          />

          {!isLogin && (
            <Input
              className={styles["auth-input"]}
              type="text"
              placeholder="用户名"
              value={username}
              onChange={(e) =>
                setUsername((e.target as HTMLInputElement).value)
              }
              required
            />
          )}

          <PasswordInput
            className={styles["auth-input"]}
            placeholder="密码"
            value={password}
            onChange={(e) => setPassword((e.target as HTMLInputElement).value)}
            required
          />

          <div className={styles["auth-actions"]}>
            <IconButton
              text={isLogin ? "登录" : "注册"}
              type="primary"
              onClick={() => handleSubmit(new Event("submit") as any)}
              disabled={authStore.isLoading}
            />
          </div>

          <div className={styles["auth-switch"]}>
            {isLogin ? "还没有账号？" : "已有账号？"}
            <button type="button" onClick={switchMode}>
              {isLogin ? "立即注册" : "立即登录"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
