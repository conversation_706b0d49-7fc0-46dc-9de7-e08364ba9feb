const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function testLogin() {
  try {
    // 查找管理员用户
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!user) {
      console.log('❌ 用户不存在');
      return;
    }

    console.log('✅ 用户存在:', {
      id: user.id,
      email: user.email,
      username: user.username,
      role: user.role,
      hashedPassword: user.password.substring(0, 20) + '...'
    });

    // 测试密码验证
    const testPassword = 'admin123';
    const isValid = await bcrypt.compare(testPassword, user.password);
    
    console.log('🔐 密码验证结果:', isValid ? '✅ 正确' : '❌ 错误');
    
    if (!isValid) {
      console.log('🔧 重新设置密码...');
      const newHashedPassword = await bcrypt.hash(testPassword, 12);
      
      await prisma.user.update({
        where: { id: user.id },
        data: { password: newHashedPassword }
      });
      
      console.log('✅ 密码已重新设置');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testLogin();
