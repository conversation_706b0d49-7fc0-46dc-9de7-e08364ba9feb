.admin-page {
  display: flex;
  height: 100vh;
  background: var(--gray);
}

.admin-sidebar {
  width: 280px;
  background: var(--white);
  border-right: var(--border-in-light);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.admin-header {
  padding: 20px;
  border-bottom: var(--border-in-light);
  background: var(--second);
}

.admin-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  
  svg {
    width: 20px;
    height: 20px;
    color: var(--primary);
  }
}

.admin-subtitle {
  font-size: 14px;
  color: var(--black);
  opacity: 0.6;
  margin: 0;
}

.admin-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.admin-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: var(--black);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  font-size: 14px;
  
  &:hover {
    background: var(--hover-color);
  }
  
  &.active {
    background: var(--primary);
    color: white;
    
    svg {
      color: white;
    }
  }
  
  svg {
    width: 16px;
    height: 16px;
    color: var(--black);
    opacity: 0.6;
  }
}

.admin-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.admin-content-header {
  padding: 20px 30px;
  border-bottom: var(--border-in-light);
  background: var(--white);
}

.admin-content-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--black);
  margin: 0 0 8px 0;
}

.admin-content-subtitle {
  font-size: 14px;
  color: var(--black);
  opacity: 0.6;
  margin: 0;
}

.admin-content-body {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  background: var(--gray);
}

.admin-back-button {
  margin-bottom: 20px;
  padding: 8px 16px;
  background: var(--second);
  border: var(--border-in-light);
  border-radius: 8px;
  color: var(--black);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  
  &:hover {
    background: var(--hover-color);
  }
  
  svg {
    width: 14px;
    height: 14px;
  }
}

.admin-card {
  background: var(--white);
  border-radius: 12px;
  border: var(--border-in-light);
  padding: 24px;
  margin-bottom: 20px;
  
  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--black);
  }
  
  p {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: var(--black);
    opacity: 0.6;
    line-height: 1.5;
  }
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.admin-stat-card {
  background: var(--white);
  border-radius: 12px;
  border: var(--border-in-light);
  padding: 20px;
  text-align: center;
  
  .stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    color: var(--black);
    opacity: 0.6;
  }
}

.admin-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    label {
      font-size: 14px;
      font-weight: 500;
      color: var(--black);
    }
    
    input, textarea, select {
      padding: 12px;
      border: var(--border-in-light);
      border-radius: 8px;
      font-size: 14px;
      background: var(--white);
      color: var(--black);
      
      &:focus {
        outline: none;
        border-color: var(--primary);
      }
    }
    
    textarea {
      resize: vertical;
      min-height: 80px;
    }
  }
  
  .form-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    
    button {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &.primary {
        background: var(--primary);
        color: white;
        
        &:hover {
          background: var(--primary-hover);
        }
      }
      
      &.secondary {
        background: var(--second);
        color: var(--black);
        
        &:hover {
          background: var(--hover-color);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .admin-page {
    flex-direction: column;
  }
  
  .admin-sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: var(--border-in-light);
  }
  
  .admin-nav {
    display: flex;
    overflow-x: auto;
    padding: 10px;
    
    .admin-nav-item {
      white-space: nowrap;
      min-width: auto;
      padding: 8px 16px;
    }
  }
  
  .admin-content-body {
    padding: 20px;
  }
  
  .admin-stats {
    grid-template-columns: 1fr;
  }
}
