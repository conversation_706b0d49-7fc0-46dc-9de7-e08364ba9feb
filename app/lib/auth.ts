import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { prisma } from "./db";
import { UserRole } from "@prisma/client";

const JWT_SECRET = process.env.JWT_SECRET || "fallback-secret-key";

export interface AuthUser {
  id: string;
  email: string;
  username: string;
  role: UserRole;
}

export interface JWTPayload {
  userId: string;
  email: string;
  username: string;
  role: UserRole;
}

// 密码加密
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

// 密码验证
export async function verifyPassword(
  password: string,
  hashedPassword: string,
): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// 生成 JWT Token
export function generateToken(user: AuthUser): string {
  const payload: JWTPayload = {
    userId: user.id,
    email: user.email,
    username: user.username,
    role: user.role,
  };

  return jwt.sign(payload, JWT_SECRET, { expiresIn: "7d" });
}

// 验证 JWT Token
export function verifyToken(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JWTPayload;
  } catch (error) {
    return null;
  }
}

// 创建用户会话
export async function createSession(userId: string, token: string) {
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 7); // 7天过期

  return prisma.session.create({
    data: {
      userId,
      token,
      expiresAt,
    },
  });
}

// 删除用户会话
export async function deleteSession(token: string) {
  return prisma.session.delete({
    where: { token },
  });
}

// 验证会话
export async function validateSession(token: string) {
  const session = await prisma.session.findUnique({
    where: { token },
    include: { user: true },
  });

  if (!session || session.expiresAt < new Date()) {
    if (session) {
      await deleteSession(token);
    }
    return null;
  }

  return session.user;
}

// 从请求头获取用户信息
export async function getUserFromRequest(
  request: Request,
): Promise<AuthUser | null> {
  const authHeader = request.headers.get("authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = verifyToken(token);

  if (!payload) {
    return null;
  }

  // 验证会话是否存在且有效
  const user = await validateSession(token);
  return user;
}

// 初始化管理员用户
export async function initializeAdmin() {
  const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
  const adminPassword = process.env.ADMIN_PASSWORD || "admin123";

  // 检查是否已存在管理员
  const existingAdmin = await prisma.user.findFirst({
    where: { role: UserRole.ADMIN },
  });

  if (existingAdmin) {
    console.log("Admin user already exists");
    return existingAdmin;
  }

  // 创建管理员用户
  const hashedPassword = await hashPassword(adminPassword);
  const admin = await prisma.user.create({
    data: {
      email: adminEmail,
      username: "admin",
      password: hashedPassword,
      role: UserRole.ADMIN,
    },
  });

  console.log(`Admin user created: ${adminEmail}`);
  return admin;
}
