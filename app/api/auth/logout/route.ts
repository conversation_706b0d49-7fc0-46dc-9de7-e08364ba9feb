import { NextRequest, NextResponse } from "next/server";
import { deleteSession } from "@/app/lib/auth";

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "未提供有效的认证信息" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);

    // 删除会话
    await deleteSession(token);

    return NextResponse.json({
      message: "登出成功",
    });
  } catch (error) {
    console.error("Logout error:", error);
    return NextResponse.json(
      { error: "登出失败，请稍后重试" },
      { status: 500 },
    );
  }
}
