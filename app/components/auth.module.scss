.auth-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--gray);
  padding: 20px;
  box-sizing: border-box;
}

.auth-container {
  background: var(--white);
  border-radius: 20px;
  box-shadow: var(--card-shadow);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  text-align: center;
  position: relative;
}

.auth-header {
  position: absolute;
  top: 20px;
  left: 20px;
}

.auth-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0 30px 0;

  svg {
    width: 48px;
    height: 48px;
    color: var(--primary);
  }
}

.auth-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--black);
  margin: 0 0 8px 0;
}

.auth-tips {
  font-size: 14px;
  color: var(--black);
  opacity: 0.6;
  margin: 0 0 30px 0;
}

.auth-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-in-light);
  border-radius: 8px;
  font-size: 14px;
  background: var(--white);
  color: var(--black);
  transition: border-color 0.2s ease;
  margin-bottom: 16px;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: var(--primary);
  }

  &::placeholder {
    color: var(--black);
    opacity: 0.4;
  }
}

.auth-input-second {
  margin-bottom: 16px;
}

.auth-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 20px;
}

.auth-error {
  background: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 16px;
  text-align: left;
}

.auth-success {
  background: #efe;
  border: 1px solid #cfc;
  color: #3c3;
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 16px;
  text-align: left;
}

.auth-switch {
  text-align: center;
  font-size: 14px;
  color: var(--black);
  opacity: 0.6;
  margin-top: 20px;

  button {
    background: none;
    border: none;
    color: var(--primary);
    cursor: pointer;
    text-decoration: underline;
    font-size: inherit;
    margin-left: 4px;

    &:hover {
      opacity: 0.8;
    }
  }
}

@media (max-width: 768px) {
  .auth-page {
    padding: 10px;
  }

  .auth-container {
    padding: 30px 20px;
    max-width: 350px;
  }
}
