import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/lib/db";
import { hashPassword, generateToken, createSession } from "@/app/lib/auth";
import { UserRole } from "@prisma/client";

export async function POST(request: NextRequest) {
  try {
    const { email, username, password } = await request.json();

    // 验证输入
    if (!email || !username || !password) {
      return NextResponse.json(
        { error: "邮箱、用户名和密码都是必填项" },
        { status: 400 },
      );
    }

    if (password.length < 6) {
      return NextResponse.json({ error: "密码长度至少为6位" }, { status: 400 });
    }

    // 检查邮箱是否已存在
    const existingUserByEmail = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUserByEmail) {
      return NextResponse.json({ error: "该邮箱已被注册" }, { status: 400 });
    }

    // 检查用户名是否已存在
    const existingUserByUsername = await prisma.user.findUnique({
      where: { username },
    });

    if (existingUserByUsername) {
      return NextResponse.json({ error: "该用户名已被使用" }, { status: 400 });
    }

    // 创建用户
    const hashedPassword = await hashPassword(password);
    const user = await prisma.user.create({
      data: {
        email,
        username,
        password: hashedPassword,
        role: UserRole.USER,
      },
    });

    // 生成 token 和创建会话
    const token = generateToken({
      id: user.id,
      email: user.email,
      username: user.username,
      role: user.role,
    });

    await createSession(user.id, token);

    return NextResponse.json({
      message: "注册成功",
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
      },
      token,
    });
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { error: "注册失败，请稍后重试" },
      { status: 500 },
    );
  }
}
