## 商业化 NextChat 后端中转一体化改造需求文档（概要版）

### 1. 项目目标
将 NextChat 由前端直连第三方服务商的模式，升级为前后端一体化、所有敏感信息后端中转、支持多用户和商业化运营的架构。

---

### 2. 架构核心

- **技术栈**：Next.js + React + TypeScript + Antd + 状态管理（如 Zustand）+ MySQL 数据库 + ORM（如 Prisma）
- **部署方式**：Next.js 一体式服务，既负责页面渲染，也负责 API 路由转发
- **安全原则**：API Key、服务商 URL 等敏感信息只在服务器端处理，前端用户不可见

---

### 3. 主要功能与改造点

#### 3.1. API 层中转
- 所有跟 AI 服务商（如 OpenAI、智谱等）交互的操作，统一通过 Next.js 的 API 路由实现后端转发
- 前端发起聊天、模型选择等请求时，全部请求自己的后端 API，由后端获取配置后再请求第三方
- 前端永远无法获得真实 API Key 和服务商地址

#### 3.2. 数据库配置
- 后端通过数据库集中管理渠道商信息、系统参数、用户数据等
- 数据库表结构可根据实际业务灵活设计，满足渠道配置、用户管理、运营需求等

#### 3.3. 管理后台
- 复用现有聊天界面框架，左侧边栏改为管理菜单项（仪表盘、服务商管理、用户管理等）
- 右侧内容区域参考全局设置页面的布局设计，实现各项管理功能
- 管理员通过后台界面统一维护和调整所有服务商配置
- 管理面板需要权限验证，只有管理员用户可以访问

#### 3.4. 用户体系与权限
- 实现完整的用户注册/登录系统，支持账号密码鉴权
- 区分管理员用户和普通用户权限
- 登录界面设计参考 NextChat 风格，简洁美观

#### 3.5. 计费与用量统计（可选/后续）
- 后端对每次请求进行用量统计，为后续计费、风控、运营提供基础

---

### 4. 实现原则

- 前端全部请求通过自有 API 层中转，杜绝任何敏感数据直达前端
- 管理后台、用户体系、计费等功能分步实现，逐渐完善
- 具体数据库表结构、API 路由、业务逻辑根据实际需求灵活设计
- 开发时注意管理模板与原模块的独立性，架构保持清晰，优先实现核心功能迁移重构确保正常运行
- 开发阶段先使用 sqlite 避免配置数据库的复杂，并且应该合理分割模块，提供环境变量，确保两种数据库之间可以进行切换而无需修复代码

---

### 5. UI/UX 设计细化

#### 5.1. 登录/注册界面
- 将左下角 GitHub 按钮改造为登录/注册按钮，样式保持简洁统一
- 登录后隐藏按钮，未登录时显示"登录/注册"文字
- 在全局设置的通用设置中，提供登出账号的配置项功能
- 登录页面采用类似 NextChat 的设计风格：
  - 中央卡片式布局
  - 包含 Logo 和欢迎文字
  - 邮箱/用户名输入框
  - 密码输入框
  - 登录/注册切换功能
  - 暂不支持 Google OAuth，专注于账密登录

#### 5.2. 管理面板入口
- 在登录/注册按钮右侧添加管理面板入口按钮
- 仅对管理员用户显示此按钮
- 点击后进入管理面板，需要验证管理员权限

#### 5.3. 管理面板界面
- 复用现有聊天界面的整体框架和布局
- 左侧边栏替换为管理菜单：
  - 仪表盘（系统概览）
  - 服务商管理（API 配置、模型管理）
  - 用户管理（用户列表、权限设置）
  - 系统设置（全局参数配置）
- 右侧内容区域参考全局设置页面的设计：
  - 顶部标签页切换（如果需要）
  - 表单式配置项布局
  - 统一的保存/取消操作
- 暂时不实现二级分页，左侧菜单点击即为页面切换
- 尽可能复用现在已有的组件，例如模型管理modal、供应商管理界面等

---

### 6. 实施优先级

#### 第一阶段（当前重点）
1. **用户认证系统**
   - 改造左下角按钮为登录/注册入口
   - 实现登录/注册页面
   - 完善用户权限验证

2. **管理面板基础框架**
   - 添加管理面板入口（仅管理员可见）
   - 复用现有界面框架，实现左侧菜单切换
   - 参考全局设置页面实现右侧内容区域

#### 第二阶段（后续扩展）
- 完善各管理功能的具体实现
- 添加用量统计和计费功能
- 优化用户体验和界面细节

---

### 7. 预期效果

- **安全**：API Key 完全后端隔离，避免泄漏
- **易用**：普通用户无需繁琐配置，开箱即用
- **可运营**：支持多渠道管理、用户分级、用量统计等商业化需求
- **可扩展**：便于后续接入新的 AI 服务商、接入计费、权限、监控等功能
- **美观**：复用现有设计语言，保持界面一致性和用户体验

