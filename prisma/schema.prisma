// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String   // 加密后的密码
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 用户会话
  sessions Session[]

  @@map("users")
}

// 用户角色枚举
enum UserRole {
  USER
  ADMIN
}

// 用户会话表（用于登录状态管理）
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  // 关联用户
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// 服务商配置表（管理面板使用）
model Provider {
  id          String      @id @default(cuid())
  name        String      @unique
  type        ProviderType
  apiKey      String
  baseUrl     String?
  isEnabled   Boolean     @default(true)
  config      Json?       // 额外配置信息
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  @@map("providers")
}

// 服务商类型枚举
enum ProviderType {
  OPENAI
  ANTHROPIC
  GOOGLE
  AZURE
  CUSTOM
}

// 系统配置表
model SystemConfig {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_configs")
}
